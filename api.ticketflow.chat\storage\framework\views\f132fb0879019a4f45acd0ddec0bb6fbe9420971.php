<!doctype html>
<html lang="pt-BR">
<?php
/** @var App\Models\Order $order */
/** @var App\Models\OrderDetail $detail */
/** @var string $logo */

use App\Helpers\ResponseError;

$userName = "{$order->user?->firstname} {$order->user?->lastname}";
$userPhone = $order->phone ?? $order->user?->phone;
$shopPhone = $order->shop?->phone ?? $order->shop?->seller?->phone;
$address = data_get($order, 'address.address', '');
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recibo #<?php echo e($order?->id); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.2;
            color: #000;
            background: #fff;
            width: 80mm; /* Largura padrão impressora térmica 80mm */
            margin: 0 auto;
            padding: 5mm;
        }

        .receipt-container {
            width: 100%;
            max-width: 70mm;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }

        .logo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0 auto 8px;
            display: block;
        }

        .shop-name {
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
            margin-bottom: 3px;
        }

        .shop-info {
            font-size: 10px;
            line-height: 1.3;
        }

        .receipt-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin: 15px 0 10px;
            text-transform: uppercase;
        }

        .order-info {
            text-align: center;
            margin-bottom: 15px;
            font-size: 11px;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }

        .order-number {
            font-weight: bold;
            font-size: 12px;
        }

        .customer-info {
            margin-bottom: 15px;
            font-size: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }

        .info-label {
            font-weight: bold;
            display: inline-block;
            width: 50px;
        }

        .items-section {
            margin-bottom: 15px;
        }

        .items-header {
            font-weight: bold;
            text-align: center;
            margin-bottom: 8px;
            font-size: 11px;
            text-transform: uppercase;
        }

        .item {
            margin-bottom: 8px;
            font-size: 10px;
            border-bottom: 1px dotted #ccc;
            padding-bottom: 5px;
        }

        .item-name {
            font-weight: bold;
            margin-bottom: 2px;
        }

        .item-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .item-qty-price {
            font-size: 10px;
        }

        .item-total {
            font-weight: bold;
            text-align: right;
        }

        .item-extras {
            font-size: 9px;
            color: #666;
            margin-left: 10px;
            font-style: italic;
        }

        .totals-section {
            margin-top: 15px;
            border-top: 1px dashed #000;
            padding-top: 10px;
            font-size: 11px;
        }

        .total-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }

        .total-line.final {
            font-weight: bold;
            font-size: 12px;
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 8px;
        }

        .payment-info {
            margin-top: 15px;
            text-align: center;
            font-size: 10px;
            border-top: 1px dashed #000;
            padding-top: 10px;
        }

        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 9px;
            border-top: 1px dashed #000;
            padding-top: 10px;
        }

        .delivery-info {
            margin-top: 10px;
            font-size: 10px;
            text-align: center;
        }

        .status-badge {
            display: inline-block;
            padding: 2px 6px;
            border: 1px solid #000;
            font-size: 9px;
            text-transform: uppercase;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .separator {
            text-align: center;
            margin: 10px 0;
            font-size: 14px;
        }

        @media  print {
            body {
                width: 80mm;
                margin: 0;
                padding: 2mm;
            }
            
            .receipt-container {
                max-width: none;
                width: 100%;
            }
        }
    </style>
</head>
<body>
<div class="receipt-container">
    <!-- Cabeçalho -->
    <div class="header">
        <img class="logo" src="<?php echo e($logo); ?>" alt="Logo"/>
        <div class="shop-name"><?php echo e($order->shop?->translation?->title ?? 'ESTABELECIMENTO'); ?></div>
        <div class="shop-info">
            <?php echo e($order->shop?->translation?->address ?? ''); ?><br>
            <?php if($shopPhone): ?>
                Tel: <?php echo e($shopPhone); ?><br>
            <?php endif; ?>
            CNPJ: <?php echo e($order->shop?->tax ?? 'N/A'); ?>

        </div>
    </div>

    <!-- Título do Recibo -->
    <div class="receipt-title">CUPOM NÃO FISCAL</div>

    <!-- Informações do Pedido -->
    <div class="order-info">
        <div class="order-number">PEDIDO #<?php echo e($order->id); ?></div>
        <div><?php echo e($order->created_at?->format('d/m/Y H:i:s')); ?></div>
    </div>

    <!-- Informações do Cliente -->
    <div class="customer-info">
        <div><span class="info-label">Cliente:</span> <?php echo e($userName); ?></div>
        <?php if($userPhone): ?>
            <div><span class="info-label">Fone:</span> <?php echo e($userPhone); ?></div>
        <?php endif; ?>
        <?php if($address): ?>
            <div><span class="info-label">Endereço:</span> <?php echo e($address); ?></div>
        <?php endif; ?>
    </div>

    <!-- Itens do Pedido -->
    <div class="items-section">
        <div class="items-header">ITENS DO PEDIDO</div>
        
        <?php $__currentLoopData = $order->orderDetails; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $orderDetail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $addons = '';
                $orderDetail->children?->transform(function ($i) use(&$addons, $order) {
                    $addons .= $i?->stock?->countable?->translation?->title . " x" . $i?->quantity . " ";
                });

                $extras = '';
                foreach($orderDetail->stock->stockExtras ?? [] as $extra) {
                    if($extra?->value) {
                        $extras .= ', ' . $extra?->value;
                    }
                }
                $extras = ltrim($extras, ', ');
            ?>
            
            <div class="item">
                <div class="item-name"><?php echo e($orderDetail->stock?->countable?->translation?->title); ?><?php echo e($extras ? ' (' . $extras . ')' : ''); ?></div>
                
                <?php if(!empty($addons)): ?>
                    <div class="item-extras">+ <?php echo e(trim($addons)); ?></div>
                <?php endif; ?>
                
                <div class="item-details">
                    <div class="item-qty-price">
                        <?php echo e($orderDetail->quantity); ?>x <?php echo e($order->currency?->symbol); ?><?php echo e(number_format($orderDetail->rate_origin_price, 2, ',', '.')); ?>

                    </div>
                    <div class="item-total">
                        <?php echo e($order->currency?->symbol); ?><?php echo e(number_format($orderDetail->rate_total_price, 2, ',', '.')); ?>

                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- Totais -->
    <div class="totals-section">
        <div class="total-line">
            <span>Subtotal:</span>
            <span><?php echo e($order->currency?->symbol); ?><?php echo e(number_format($order->origin_price, 2, ',', '.')); ?></span>
        </div>
        
        <?php if($order->rate_tax > 0): ?>
            <div class="total-line">
                <span>Taxa de Serviço:</span>
                <span><?php echo e($order->currency?->symbol); ?><?php echo e(number_format($order->rate_tax, 2, ',', '.')); ?></span>
            </div>
        <?php endif; ?>
        
        <?php if($order->rate_delivery_fee > 0): ?>
            <div class="total-line">
                <span>Taxa de Entrega:</span>
                <span><?php echo e($order->currency?->symbol); ?><?php echo e(number_format($order->rate_delivery_fee, 2, ',', '.')); ?></span>
            </div>
        <?php endif; ?>
        
        <?php if($order->rate_coupon_price > 0): ?>
            <div class="total-line">
                <span>Desconto (Cupom):</span>
                <span>-<?php echo e($order->currency?->symbol); ?><?php echo e(number_format($order->rate_coupon_price, 2, ',', '.')); ?></span>
            </div>
        <?php endif; ?>
        
        <?php if($order->rate_total_discount > 0): ?>
            <div class="total-line">
                <span>Desconto:</span>
                <span>-<?php echo e($order->currency?->symbol); ?><?php echo e(number_format($order->rate_total_discount, 2, ',', '.')); ?></span>
            </div>
        <?php endif; ?>
        
        <div class="total-line final">
            <span>TOTAL:</span>
            <span><?php echo e($order->currency?->symbol); ?><?php echo e(number_format($order->rate_total_price, 2, ',', '.')); ?></span>
        </div>
    </div>

    <!-- Informações de Pagamento -->
    <div class="payment-info">
        <div><strong>FORMA DE PAGAMENTO</strong></div>
        <div><?php echo e($order->transaction?->paymentSystem?->tag ?? 'N/A'); ?></div>
        <div class="status-badge"><?php echo e($order->transaction?->status ?? 'PENDENTE'); ?></div>
    </div>

    <!-- Informações de Entrega -->
    <?php if($order->delivery_date || $order->delivery_time): ?>
        <div class="delivery-info">
            <div><strong>ENTREGA</strong></div>
            <?php if($order->delivery_date): ?>
                <div>Data: <?php echo e(\Carbon\Carbon::parse($order->delivery_date)->format('d/m/Y')); ?></div>
            <?php endif; ?>
            <?php if($order->delivery_time): ?>
                <div>Horário: <?php echo e($order->delivery_time); ?></div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Separador -->
    <div class="separator">= = = = = = = = = = = = = =</div>

    <!-- Rodapé -->
    <div class="footer">
        <div>OBRIGADO PELA PREFERÊNCIA!</div>
        <div><?php echo e(date('d/m/Y H:i:s')); ?></div>
        <div>Este não é um documento fiscal</div>
    </div>

    <!-- Espaço para corte -->
    <div style="height: 20px;"></div>
</div>
</body>
</html>
<?php /**PATH C:\OSPanel\home\api.ticketflow.chat\resources\views/order-invoice.blade.php ENDPATH**/ ?>